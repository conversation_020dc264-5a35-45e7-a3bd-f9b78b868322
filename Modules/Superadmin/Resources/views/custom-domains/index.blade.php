@extends('layouts.app')
@section('title', 'Custom Domains')

@push('css')
<style>
/* Modern Custom Domain Styles */
.page-header-modern {
    background: linear-gradient(135deg, #667eea 0%, #764ba2 100%);
    color: white;
    padding: 30px 20px;
    border-radius: 15px;
    margin-bottom: 30px;
}

.page-header-modern h1 {
    color: white;
    margin-bottom: 5px;
    font-size: 2.5rem;
}

.page-header-modern p {
    color: rgba(255,255,255,0.8);
    margin-bottom: 0;
    font-size: 1.1rem;
}

.domain-stats {
    background: white;
    border-radius: 15px;
    padding: 25px;
    margin-bottom: 30px;
    box-shadow: 0 4px 20px rgba(0,0,0,0.1);
    border: 1px solid #e9ecef;
}

.stat-item {
    text-align: center;
    padding: 20px 10px;
}

.stat-number {
    font-size: 2.5rem;
    font-weight: bold;
    color: #667eea;
    display: block;
    line-height: 1;
}

.stat-label {
    color: #6c757d;
    font-size: 0.85rem;
    text-transform: uppercase;
    letter-spacing: 1px;
    margin-top: 8px;
}

.domain-table-card {
    background: white;
    border-radius: 15px;
    box-shadow: 0 4px 20px rgba(0,0,0,0.1);
    overflow: hidden;
    border: 1px solid #e9ecef;
}

.modern-btn {
    border-radius: 20px;
    padding: 8px 20px;
    font-weight: 600;
    font-size: 0.85rem;
    transition: all 0.3s ease;
    border: none;
}

.modern-btn-primary {
    background: linear-gradient(135deg, #667eea 0%, #764ba2 100%);
    color: white;
}

.modern-btn-primary:hover {
    transform: translateY(-2px);
    box-shadow: 0 5px 15px rgba(102, 126, 234, 0.4);
    color: white;
}

.status-badge {
    padding: 6px 12px;
    border-radius: 15px;
    font-size: 0.75rem;
    font-weight: 600;
    text-transform: uppercase;
    letter-spacing: 0.5px;
    display: inline-block;
}

.status-verified {
    background: #28a745;
    color: white;
}

.status-pending {
    background: #ffc107;
    color: #212529;
}

.ssl-active {
    background: #17a2b8;
    color: white;
}

.ssl-pending {
    background: #fd7e14;
    color: white;
}

.empty-state {
    text-align: center;
    padding: 60px 20px;
    background: linear-gradient(135deg, #667eea 0%, #764ba2 100%);
    border-radius: 15px;
    color: white;
    margin: 30px 0;
}

.empty-state-icon {
    font-size: 4rem;
    margin-bottom: 20px;
    opacity: 0.8;
}

.page-header-modern {
    background: linear-gradient(135deg, #667eea 0%, #764ba2 100%);
    color: white;
    padding: 30px 20px;
    border-radius: 15px;
    margin-bottom: 30px;
}

.page-header-modern h1 {
    color: white;
    margin-bottom: 5px;
}

.page-header-modern p {
    color: rgba(255,255,255,0.8);
    margin-bottom: 0;
}

/* Table improvements */
.table th {
    border-top: none;
    font-weight: 600;
    color: #495057;
    font-size: 0.85rem;
}

.table td {
    vertical-align: middle;
    padding: 15px 12px;
}

/* Button group improvements */
.btn-group .btn {
    margin-right: 5px;
}

.btn-group .btn:last-child {
    margin-right: 0;
}

/* Modal improvements */
.modal-content {
    border-radius: 15px;
    border: none;
    box-shadow: 0 10px 40px rgba(0,0,0,0.2);
}

.modal-header {
    border-bottom: 1px solid rgba(255,255,255,0.2);
}

.modal-footer {
    border-top: none;
}

/* Form improvements */
.form-control {
    border-radius: 10px;
    border: 2px solid #e9ecef;
    padding: 12px 15px;
}

.form-control:focus {
    border-color: #667eea;
    box-shadow: 0 0 0 0.2rem rgba(102, 126, 234, 0.25);
}

.input-group-text {
    border-radius: 10px 0 0 10px;
    border: 2px solid #e9ecef;
    border-right: none;
    background: #f8f9fa;
}

/* Progress Modal Styles */
.toastr-progress-modal {
    width: 500px !important;
    border-radius: 15px !important;
    box-shadow: 0 10px 40px rgba(0,0,0,0.2) !important;
}

.toastr-progress-modal .toast-message {
    padding: 0 !important;
    margin: 0 !important;
}

.step-item {
    transition: all 0.3s ease;
}

.step-item i.fa-check {
    animation: checkmark 0.5s ease-in-out;
}

@keyframes checkmark {
    0% { transform: scale(0); }
    50% { transform: scale(1.2); }
    100% { transform: scale(1); }
}

/* Responsive improvements */
@media (max-width: 768px) {
    .page-header-modern {
        padding: 20px 15px;
        text-align: center;
    }

    .domain-stats {
        margin: 15px;
    }

    .domain-table-card {
        margin: 15px;
    }

    .toastr-progress-modal {
        width: 90% !important;
        margin: 0 auto !important;
    }
}
</style>
@endpush

@section('content')
<section class="content-header">
    <div class="page-header-modern">
        <div class="row">
            <div class="col-md-8">
                <h1>
                    <i class="fas fa-globe"></i> Custom Domains
                </h1>
                <p>Professional branded domains for your business</p>
            </div>
            <div class="col-md-4 text-right">
                @if($canAddMore)
                <button class="btn modern-btn modern-btn-primary btn-lg" id="add-domain-btn">
                    <i class="fas fa-plus"></i> Add Domain
                </button>
                @else
                <button class="btn btn-light btn-lg" disabled>
                    <i class="fas fa-lock"></i> Limit Reached
                </button>
                @endif
            </div>
        </div>
    </div>
</section>

<section class="content">
    <!-- Modern Stats Section -->
    <div class="domain-stats">
        <div class="row">
            <div class="col-md-3 col-sm-6">
                <div class="stat-item">
                    <span class="stat-number">{{ $currentCount }}</span>
                    <span class="stat-label">Total Domains</span>
                </div>
            </div>
            <div class="col-md-3 col-sm-6">
                <div class="stat-item">
                    <span class="stat-number text-success" id="active-count">-</span>
                    <span class="stat-label">Active</span>
                </div>
            </div>
            <div class="col-md-3 col-sm-6">
                <div class="stat-item">
                    <span class="stat-number text-info" id="ssl-count">-</span>
                    <span class="stat-label">SSL Secured</span>
                </div>
            </div>
            <div class="col-md-3 col-sm-6">
                <div class="stat-item">
                    <span class="stat-number text-warning">{{ $maxDomains - $currentCount }}</span>
                    <span class="stat-label">Remaining</span>
                </div>
            </div>
        </div>
    </div>

    <!-- Domains Table -->
    <div class="row">
        <div class="col-xs-12">
            <div class="domain-table-card">
                <div style="padding: 25px;">
                    <div class="row" style="margin-bottom: 20px;">
                        <div class="col-md-8">
                            <h3 style="margin: 0;">
                                <i class="fas fa-list text-primary"></i> Your Custom Domains
                            </h3>
                        </div>
                        <div class="col-md-4 text-right">
                            <button class="btn modern-btn modern-btn-primary" id="refresh-all-status">
                                <i class="fas fa-sync"></i> Refresh All
                            </button>
                        </div>
                    </div>

                    @if($currentCount == 0)
                    <div class="empty-state">
                        <div class="empty-state-icon">
                            <i class="fas fa-globe"></i>
                        </div>
                        <h3>No Custom Domains Yet</h3>
                        <p style="margin-bottom: 30px;">Add your first custom domain to get started with professional branded access to your system.</p>
                        @if($canAddMore)
                        <button class="btn btn-light btn-lg modern-btn" id="add-first-domain-btn">
                            <i class="fas fa-plus"></i> Add Your First Domain
                        </button>
                        @endif
                    </div>
                    @else
                    <div class="table-responsive">
                        <table class="table table-hover" id="custom_domains_table">
                            <thead>
                                <tr>
                                    <th>
                                        <i class="fas fa-globe"></i> Domain
                                    </th>
                                    <th>
                                        <i class="fas fa-check-circle"></i> Status
                                    </th>
                                    <th>
                                        <i class="fas fa-lock"></i> SSL
                                    </th>
                                    <th>
                                        <i class="fas fa-calendar"></i> Added
                                    </th>
                                    <th width="280px">
                                        <i class="fas fa-cogs"></i> Actions
                                    </th>
                                </tr>
                            </thead>
                        </table>
                    </div>
                    @endif
                </div>
            </div>
        </div>
    </div>
</section>

<!-- Modern Add Domain Modal -->
<div class="modal fade" id="add-domain-modal" tabindex="-1" role="dialog">
    <div class="modal-dialog modal-lg" role="document">
        <div class="modal-content">
            <div class="modal-header" style="background: linear-gradient(135deg, #667eea 0%, #764ba2 100%); color: white; border: none;">
                <h4 class="modal-title">
                    <i class="fas fa-globe"></i> Add Custom Domain
                </h4>
                <button type="button" class="close" data-dismiss="modal" style="color: white; opacity: 1;">
                    <span>&times;</span>
                </button>
            </div>
            <form id="add-domain-form">
                <div class="modal-body" style="padding: 30px;">
                    <div class="row">
                        <div class="col-md-8">
                            <div class="form-group">
                                <label class="control-label" style="font-weight: 600; color: #333; margin-bottom: 10px;">
                                    <i class="fas fa-globe text-primary"></i> Your Custom Domain
                                    <span class="text-danger">*</span>
                                </label>
                                <div class="input-group input-group-lg">
                                    <span class="input-group-addon">
                                        <i class="fas fa-globe text-primary"></i>
                                    </span>
                                    <input type="text" name="custom_domain" id="custom_domain"
                                           class="form-control"
                                           placeholder="pos.yourcompany.com">
                                </div>
                                <small class="text-muted" style="margin-top: 8px; display: block;">
                                    <i class="fas fa-info-circle"></i>
                                    Any domain extension allowed (.com, .net, .org, .co.uk, .xyz, etc.)
                                </small>
                            </div>
                        </div>
                        <div class="col-md-4">
                            <div class="info-box bg-green">
                                <span class="info-box-icon">
                                    <i class="fas fa-magic"></i>
                                </span>
                                <div class="info-box-content">
                                    <span class="info-box-text">Auto Setup</span>
                                    <span class="info-box-number">Available</span>
                                </div>
                            </div>
                        </div>
                    </div>

                    <div class="alert alert-info" style="background: linear-gradient(135deg, #667eea 0%, #764ba2 100%); color: white; border: none; border-radius: 10px; margin-top: 20px;">
                        <h5><i class="fas fa-rocket"></i> Super Simple Process</h5>
                        <div class="row text-center" style="margin-top: 20px;">
                            <div class="col-md-3">
                                <div style="padding: 15px;">
                                    <i class="fas fa-plus-circle fa-2x" style="margin-bottom: 10px; opacity: 0.8;"></i>
                                    <h6 style="font-weight: bold;">1. Add Domain</h6>
                                    <small style="opacity: 0.8;">Enter your domain</small>
                                </div>
                            </div>
                            <div class="col-md-3">
                                <div style="padding: 15px;">
                                    <i class="fas fa-cog fa-2x" style="margin-bottom: 10px; opacity: 0.8;"></i>
                                    <h6 style="font-weight: bold;">2. Set DNS</h6>
                                    <small style="opacity: 0.8;">Point to our server</small>
                                </div>
                            </div>
                            <div class="col-md-3">
                                <div style="padding: 15px;">
                                    <i class="fas fa-magic fa-2x" style="margin-bottom: 10px; opacity: 0.8;"></i>
                                    <h6 style="font-weight: bold;">3. Auto Setup</h6>
                                    <small style="opacity: 0.8;">We handle everything</small>
                                </div>
                            </div>
                            <div class="col-md-3">
                                <div style="padding: 15px;">
                                    <i class="fas fa-check-circle fa-2x" style="margin-bottom: 10px; opacity: 0.8;"></i>
                                    <h6 style="font-weight: bold;">4. Ready!</h6>
                                    <small style="opacity: 0.8;">Domain is live</small>
                                </div>
                            </div>
                        </div>
                    </div>
                </div>
                <div class="modal-footer" style="border-top: none; padding: 20px 30px;">
                    <button type="button" class="btn btn-default btn-lg" data-dismiss="modal">
                        <i class="fas fa-times"></i> Cancel
                    </button>
                    <button type="submit" class="btn modern-btn modern-btn-primary btn-lg">
                        <i class="fas fa-plus"></i> Add Domain
                    </button>
                </div>
            </form>
        </div>
    </div>
</div>

<!-- Modern DNS Instructions Modal -->
<div class="modal fade" id="dns-instructions-modal" tabindex="-1" role="dialog">
    <div class="modal-dialog modal-xl" role="document">
        <div class="modal-content" style="border-radius: 20px; border: none; overflow: hidden;">
            <div class="modal-header" style="background: linear-gradient(135deg, #667eea 0%, #764ba2 100%); color: white; border: none; padding: 25px;">
                <h4 class="modal-title" style="margin: 0;">
                    <i class="fas fa-rocket"></i> DNS Setup Instructions
                </h4>
                <button type="button" class="close" data-dismiss="modal" style="color: white; opacity: 1; font-size: 1.5rem;">
                    <span>&times;</span>
                </button>
            </div>
            <div class="modal-body" id="dns-instructions-content" style="padding: 30px;">
                <!-- Content will be loaded dynamically -->
            </div>
            <div class="modal-footer" style="border-top: none; padding: 25px;">
                <button type="button" class="btn btn-lg modern-btn modern-btn-primary" data-dismiss="modal">
                    <i class="fas fa-check"></i> Got It!
                </button>
            </div>
        </div>
    </div>
</div>

@endsection

@section('javascript')
<script>
$(document).ready(function() {
    // Initialize DataTable only if domains exist
    @if($currentCount > 0)
    var table = $('#custom_domains_table').DataTable({
        processing: true,
        serverSide: true,
        ajax: "{{ route('business.custom-domains.index') }}",
        columns: [
            {data: 'custom_domain', name: 'custom_domain'},
            {data: 'status', name: 'status', orderable: false, searchable: false},
            {data: 'ssl_status', name: 'ssl_status', orderable: false, searchable: false},
            {data: 'verified_at', name: 'verified_at'},
            {data: 'actions', name: 'actions', orderable: false, searchable: false}
        ],
        order: [[3, 'desc']],
        language: {
            processing: '<i class="fas fa-spinner fa-spin"></i> Loading domains...'
        },
        drawCallback: function() {
            updateStats();
        }
    });
    @endif

    // Add Domain Modal
    $('#add-domain-btn, #add-first-domain-btn').click(function() {
        $('#add-domain-modal').modal('show');
    });

    // Add Domain Form Submit
    $('#add-domain-form').submit(function(e) {
        e.preventDefault();
        
        var formData = {
            custom_domain: $('#custom_domain').val(),
            _token: '{{ csrf_token() }}'
        };

        $.ajax({
            url: "{{ route('business.custom-domains.store') }}",
            type: 'POST',
            data: formData,
            success: function(response) {
                if (response.success) {
                    $('#add-domain-modal').modal('hide');
                    $('#add-domain-form')[0].reset();
                    @if($currentCount > 0)
                    table.ajax.reload();
                    @else
                    location.reload(); // Reload page if this was the first domain
                    @endif
                    toastr.success(response.message);
                } else {
                    toastr.error(response.message);
                }
            },
            error: function(xhr) {
                var errors = xhr.responseJSON.errors;
                if (errors) {
                    Object.keys(errors).forEach(function(key) {
                        toastr.error(errors[key][0]);
                    });
                } else {
                    toastr.error('An error occurred while adding the domain.');
                }
            }
        });
    });

    // Auto Configure Domain with SSL
    $(document).on('click', '.auto-configure', function() {
        var domainId = $(this).data('id');
        var btn = $(this);
        var originalHtml = btn.html();

        // Show loading state
        btn.prop('disabled', true).html('<i class="fas fa-spinner fa-spin"></i> Setting up SSL...');

        // Show modern progress notification with steps
        var progressHtml = `
            <div style="text-align: center; padding: 20px;">
                <div style="background: linear-gradient(135deg, #667eea 0%, #764ba2 100%); color: white; padding: 20px; border-radius: 15px; margin-bottom: 20px;">
                    <h4 style="margin: 0 0 10px 0;"><i class="fas fa-magic"></i> Auto Setup in Progress</h4>
                    <p style="margin: 0; opacity: 0.9;">Setting up your domain with automatic SSL certificate...</p>
                </div>
                <div class="progress" style="height: 8px; border-radius: 10px; background: #e9ecef; margin-bottom: 20px;">
                    <div class="progress-bar" style="background: linear-gradient(135deg, #667eea 0%, #764ba2 100%); width: 0%; transition: width 0.5s ease; border-radius: 10px;"></div>
                </div>
                <div id="progress-steps" style="text-align: left;">
                    <div class="step-item" data-step="1" style="padding: 8px 0; color: #6c757d;">
                        <i class="fas fa-spinner fa-spin text-primary"></i> Checking domain pointing...
                    </div>
                    <div class="step-item" data-step="2" style="padding: 8px 0; color: #6c757d;">
                        <i class="fas fa-clock text-muted"></i> Creating Apache configuration...
                    </div>
                    <div class="step-item" data-step="3" style="padding: 8px 0; color: #6c757d;">
                        <i class="fas fa-clock text-muted"></i> Issuing SSL certificate...
                    </div>
                    <div class="step-item" data-step="4" style="padding: 8px 0; color: #6c757d;">
                        <i class="fas fa-clock text-muted"></i> Activating domain...
                    </div>
                </div>
            </div>
        `;

        toastr.info(progressHtml, '', {
            timeOut: 0,
            extendedTimeOut: 0,
            closeButton: false,
            allowHtml: true,
            positionClass: 'toast-top-center',
            toastClass: 'toastr-progress-modal'
        });

        // Simulate progress steps
        setTimeout(() => {
            $('.progress-bar').css('width', '25%');
            $('.step-item[data-step="1"] i').removeClass('fa-spinner fa-spin text-primary').addClass('fa-check text-success');
            $('.step-item[data-step="2"] i').removeClass('fa-clock text-muted').addClass('fa-spinner fa-spin text-primary');
        }, 1000);

        setTimeout(() => {
            $('.progress-bar').css('width', '50%');
            $('.step-item[data-step="2"] i').removeClass('fa-spinner fa-spin text-primary').addClass('fa-check text-success');
            $('.step-item[data-step="3"] i').removeClass('fa-clock text-muted').addClass('fa-spinner fa-spin text-primary');
        }, 2000);

        setTimeout(() => {
            $('.progress-bar').css('width', '75%');
            $('.step-item[data-step="3"] i').removeClass('fa-spinner fa-spin text-primary').addClass('fa-check text-success');
            $('.step-item[data-step="4"] i').removeClass('fa-clock text-muted').addClass('fa-spinner fa-spin text-primary');
        }, 3000);

        $.ajax({
            url: "{{ url('business/custom-domains') }}/" + domainId + "/auto-configure",
            type: 'POST',
            data: {_token: '{{ csrf_token() }}'},
            success: function(response) {
                // Clear the waiting notification
                toastr.clear();

                if (response.success) {
                    // Complete the progress
                    $('.progress-bar').css('width', '100%');
                    $('.step-item[data-step="4"] i').removeClass('fa-spinner fa-spin text-primary').addClass('fa-check text-success');

                    setTimeout(() => {
                        // Clear progress and show success
                        toastr.clear();

                        var successHtml = `
                            <div style="text-align: center; padding: 20px;">
                                <div style="background: linear-gradient(135deg, #11998e 0%, #38ef7d 100%); color: white; padding: 25px; border-radius: 15px; margin-bottom: 15px;">
                                    <i class="fas fa-check-circle fa-3x" style="margin-bottom: 15px;"></i>
                                    <h3 style="margin: 0 0 10px 0;">Domain Configured Successfully!</h3>
                                    <p style="margin: 0; opacity: 0.9;">${response.message}</p>
                                </div>
                                ${response.ssl_details ? `
                                <div style="background: #f8f9fa; padding: 15px; border-radius: 10px; border-left: 4px solid #17a2b8;">
                                    <strong><i class="fas fa-lock text-info"></i> SSL Status:</strong> ${response.ssl_details.message}
                                </div>
                                ` : ''}
                            </div>
                        `;

                        toastr.success(successHtml, '', {
                            timeOut: 10000,
                            allowHtml: true,
                            positionClass: 'toast-top-center',
                            toastClass: 'toastr-progress-modal'
                        });

                        @if($currentCount > 0)
                        table.ajax.reload();
                        @endif
                        updateStats();
                    }, 1000);
                } else {
                    if (response.step === 'dns_check') {
                        showDnsInstructions(response.instructions.domain, response.instructions);
                        toastr.warning(response.message, 'DNS Setup Required');
                    } else {
                        toastr.error(response.message, 'Configuration Failed');
                    }
                }
            },
            error: function(xhr) {
                toastr.clear();
                var errorMsg = 'An error occurred during auto-configuration.';
                if (xhr.responseJSON && xhr.responseJSON.message) {
                    errorMsg = xhr.responseJSON.message;
                }
                toastr.error(errorMsg, 'Error');
            },
            complete: function() {
                btn.prop('disabled', false).html(originalHtml);
            }
        });
    });

    // Check Domain Status
    $(document).on('click', '.check-status', function() {
        var domainId = $(this).data('id');
        var btn = $(this);

        btn.prop('disabled', true).html('<i class="fas fa-spinner fa-spin"></i> Checking...');

        $.ajax({
            url: "{{ url('business/custom-domains') }}/" + domainId + "/check-status",
            type: 'GET',
            success: function(response) {
                if (response.success) {
                    var status = response.status;
                    var message = status.pointing ?
                        'Domain is pointing correctly to our server!' :
                        'Domain is not pointing to our server yet.';

                    if (status.pointing) {
                        toastr.success(message);
                    } else {
                        toastr.warning(message + ' Server IP: ' + status.server_ip);
                    }

                    @if($currentCount > 0)
                    table.ajax.reload();
                    @endif
                } else {
                    toastr.error(response.message);
                }
            },
            error: function() {
                toastr.error('An error occurred during status check.');
            },
            complete: function() {
                btn.prop('disabled', false).html('<i class="fas fa-sync"></i> Check Status');
            }
        });
    });

    // View DNS Instructions
    $(document).on('click', '.view-instructions', function() {
        var domainId = $(this).data('id');
        
        $.ajax({
            url: "{{ url('business/custom-domains') }}/" + domainId + "/dns-instructions",
            type: 'GET',
            success: function(response) {
                if (response.success) {
                    showDnsInstructions(response.domain, response.instructions);
                } else {
                    toastr.error(response.message);
                }
            },
            error: function() {
                toastr.error('An error occurred while loading DNS instructions.');
            }
        });
    });

    // Issue SSL Certificate
    $(document).on('click', '.issue-ssl', function() {
        var domainId = $(this).data('id');
        var btn = $(this);
        
        btn.prop('disabled', true).html('<i class="fas fa-spinner fa-spin"></i> Processing...');
        
        $.ajax({
            url: "{{ url('business/custom-domains') }}/" + domainId + "/issue-ssl",
            type: 'POST',
            data: {_token: '{{ csrf_token() }}'},
            success: function(response) {
                if (response.success) {
                    toastr.success(response.message);
                    table.ajax.reload();
                } else {
                    toastr.error(response.message);
                }
            },
            error: function() {
                toastr.error('An error occurred while issuing SSL certificate.');
            },
            complete: function() {
                btn.prop('disabled', false).html('<i class="fas fa-lock"></i> Issue SSL');
            }
        });
    });

    // Delete Domain
    $(document).on('click', '.delete-domain', function() {
        var domainId = $(this).data('id');
        
        swal({
            title: 'Are you sure?',
            text: 'This will permanently delete the custom domain.',
            icon: 'warning',
            buttons: {
                cancel: {
                    text: 'Cancel',
                    value: null,
                    visible: true,
                    className: 'btn-default'
                },
                confirm: {
                    text: 'Delete',
                    value: true,
                    visible: true,
                    className: 'btn-danger'
                }
            }
        }).then((willDelete) => {
            if (willDelete) {
                $.ajax({
                    url: "{{ url('business/custom-domains') }}/" + domainId,
                    type: 'DELETE',
                    data: {_token: '{{ csrf_token() }}'},
                    success: function(response) {
                        if (response.success) {
                            toastr.success(response.message);
                            table.ajax.reload();
                        } else {
                            toastr.error(response.message);
                        }
                    },
                    error: function() {
                        toastr.error('An error occurred while deleting the domain.');
                    }
                });
            }
        });
    });

    function showDnsInstructions(domain, instructions) {
        var serverIp = instructions.server_ip;

        // Remove subdomain examples - show clean domain examples
        var cleanDomain = domain.replace(/^(pos\.|app\.|shop\.|admin\.)/, '');
        var exampleDomain = cleanDomain.includes('example') ? 'yourcompany.com' : cleanDomain;

        var content = `
            <div style="background: linear-gradient(135deg, #667eea 0%, #764ba2 100%); color: white; padding: 25px; border-radius: 15px; margin-bottom: 25px; text-align: center;">
                <h3 style="margin: 0; font-size: 1.8rem;">
                    <i class="fas fa-rocket"></i> Professional DNS Setup
                </h3>
                <p style="margin: 10px 0 0 0; opacity: 0.9;">
                    Get <strong>FREE SSL certificate</strong> for <strong>${exampleDomain}</strong>
                </p>
            </div>

            <div class="row">
                <div class="col-md-6">
                    <div style="background: white; border-radius: 15px; box-shadow: 0 4px 15px rgba(0,0,0,0.1); overflow: hidden; margin-bottom: 20px;">
                        <div style="background: linear-gradient(135deg, #11998e 0%, #38ef7d 100%); color: white; padding: 20px;">
                            <h4 style="margin: 0;">
                                <i class="fas fa-shield-alt"></i> Option 1: Cloudflare (Recommended)
                            </h4>
                        </div>
                        <div style="padding: 20px;">
                            <div style="margin-bottom: 15px;">
                                <span style="background: #28a745; color: white; padding: 4px 8px; border-radius: 12px; font-size: 0.8rem; margin-right: 10px;">✓</span>
                                <strong>FREE SSL Certificate</strong>
                            </div>
                            <div style="margin-bottom: 15px;">
                                <span style="background: #28a745; color: white; padding: 4px 8px; border-radius: 12px; font-size: 0.8rem; margin-right: 10px;">✓</span>
                                <strong>Global CDN & Speed</strong>
                            </div>
                            <div style="margin-bottom: 15px;">
                                <span style="background: #28a745; color: white; padding: 4px 8px; border-radius: 12px; font-size: 0.8rem; margin-right: 10px;">✓</span>
                                <strong>DDoS Protection</strong>
                            </div>

                            <h6 style="margin-top: 20px; color: #495057;">Simple Steps:</h6>
                            <ol style="padding-left: 20px; color: #6c757d;">
                                <li>Sign up at <a href="https://cloudflare.com" target="_blank" style="color: #667eea;">cloudflare.com</a> (FREE)</li>
                                <li>Add <strong>${exampleDomain}</strong> to Cloudflare</li>
                                <li>Set A record: <code style="background: #f8f9fa; padding: 2px 6px; border-radius: 4px;">${exampleDomain}</code> → <code style="background: #f8f9fa; padding: 2px 6px; border-radius: 4px;">${serverIp}</code></li>
                                <li>Enable "Proxied" (orange cloud icon)</li>
                                <li>SSL/TLS → "Full" mode</li>
                                <li><strong>Done! FREE SSL active!</strong></li>
                            </ol>
                        </div>
                    </div>
                </div>
                <div class="col-md-6">
                    <div style="background: white; border-radius: 15px; box-shadow: 0 4px 15px rgba(0,0,0,0.1); overflow: hidden; margin-bottom: 20px;">
                        <div style="background: linear-gradient(135deg, #667eea 0%, #764ba2 100%); color: white; padding: 20px;">
                            <h4 style="margin: 0;">
                                <i class="fas fa-globe"></i> Option 2: Direct DNS
                            </h4>
                        </div>
                        <div style="padding: 20px;">
                            <div style="background: #fff3cd; border: 1px solid #ffeaa7; border-radius: 8px; padding: 12px; margin-bottom: 15px;">
                                <small><i class="fas fa-exclamation-triangle text-warning"></i> <strong>HTTP Only</strong> - No automatic SSL</small>
                            </div>

                            <table class="table table-bordered" style="margin-bottom: 15px;">
                                <thead style="background: #f8f9fa;">
                                    <tr>
                                        <th style="border: 1px solid #dee2e6; padding: 8px;">Type</th>
                                        <th style="border: 1px solid #dee2e6; padding: 8px;">Name</th>
                                        <th style="border: 1px solid #dee2e6; padding: 8px;">Value</th>
                                    </tr>
                                </thead>
                                <tbody>
                                    <tr>
                                        <td style="border: 1px solid #dee2e6; padding: 8px;"><strong>A</strong></td>
                                        <td style="border: 1px solid #dee2e6; padding: 8px;"><code>${exampleDomain}</code></td>
                                        <td style="border: 1px solid #dee2e6; padding: 8px;"><code>${serverIp}</code></td>
                                    </tr>
                                </tbody>
                            </table>

                            <div style="background: #f8d7da; border: 1px solid #f5c6cb; border-radius: 8px; padding: 12px;">
                                <small><i class="fas fa-info-circle"></i> For SSL, use Cloudflare or contact support</small>
                            </div>
                        </div>
                    </div>
                </div>
            </div>

            <div style="background: linear-gradient(135deg, #4facfe 0%, #00f2fe 100%); color: white; padding: 20px; border-radius: 15px;">
                <h5 style="margin: 0 0 15px 0;">
                    <i class="fas fa-star"></i> Why Choose Cloudflare?
                </h5>
                <div class="row">
                    <div class="col-md-3 text-center">
                        <i class="fas fa-lock fa-2x" style="margin-bottom: 10px; opacity: 0.9;"></i>
                        <p style="margin: 0; font-size: 0.9rem;"><strong>FREE SSL</strong></p>
                    </div>
                    <div class="col-md-3 text-center">
                        <i class="fas fa-tachometer-alt fa-2x" style="margin-bottom: 10px; opacity: 0.9;"></i>
                        <p style="margin: 0; font-size: 0.9rem;"><strong>Faster Speed</strong></p>
                    </div>
                    <div class="col-md-3 text-center">
                        <i class="fas fa-shield-alt fa-2x" style="margin-bottom: 10px; opacity: 0.9;"></i>
                        <p style="margin: 0; font-size: 0.9rem;"><strong>DDoS Protection</strong></p>
                    </div>
                    <div class="col-md-3 text-center">
                        <i class="fas fa-users fa-2x" style="margin-bottom: 10px; opacity: 0.9;"></i>
                        <p style="margin: 0; font-size: 0.9rem;"><strong>Used by Millions</strong></p>
                    </div>
                </div>
            </div>
        `;

        $('#dns-instructions-content').html(content);
        $('#dns-instructions-modal').modal('show');
    }

    function updateStats() {
        // This would be updated based on actual data from the table
        // For now, we'll use placeholder logic
        setTimeout(function() {
            var data = table.data();
            var activeCount = 0;
            var sslCount = 0;
            
            data.each(function(row) {
                if (row.status && row.status.includes('Active')) {
                    activeCount++;
                }
                if (row.ssl_status && row.ssl_status.includes('Active')) {
                    sslCount++;
                }
            });
            
            $('#active-count').text(activeCount);
            $('#ssl-count').text(sslCount);
        }, 100);
    }
});
</script>
@endsection
