<div class="pos-tab-content">
    <style>
    .custom-domain-header {
        background: linear-gradient(135deg, #667eea 0%, #764ba2 100%);
        color: white;
        padding: 30px;
        border-radius: 15px;
        margin-bottom: 30px;
        text-align: center;
    }

    .domain-stats-modern {
        background: white;
        border-radius: 15px;
        padding: 25px;
        margin-bottom: 30px;
        box-shadow: 0 4px 20px rgba(0,0,0,0.1);
        border: 1px solid #e9ecef;
    }

    .stat-item-modern {
        text-align: center;
        padding: 20px 10px;
    }

    .stat-number-modern {
        font-size: 2.5rem;
        font-weight: bold;
        color: #667eea;
        display: block;
        line-height: 1;
    }

    .stat-label-modern {
        color: #6c757d;
        font-size: 0.85rem;
        text-transform: uppercase;
        letter-spacing: 1px;
        margin-top: 8px;
    }

    .domain-table-modern {
        background: white;
        border-radius: 15px;
        box-shadow: 0 4px 20px rgba(0,0,0,0.1);
        overflow: hidden;
        border: 1px solid #e9ecef;
    }

    .modern-btn-primary {
        background: linear-gradient(135deg, #667eea 0%, #764ba2 100%);
        border: none;
        border-radius: 20px;
        padding: 10px 25px;
        font-weight: 600;
        color: white;
        transition: all 0.3s ease;
    }

    .modern-btn-primary:hover {
        transform: translateY(-2px);
        box-shadow: 0 5px 15px rgba(102, 126, 234, 0.4);
        color: white;
    }

    .empty-state-modern {
        text-align: center;
        padding: 60px 20px;
        background: linear-gradient(135deg, #667eea 0%, #764ba2 100%);
        border-radius: 15px;
        color: white;
        margin: 30px 0;
    }

    .empty-state-icon-modern {
        font-size: 4rem;
        margin-bottom: 20px;
        opacity: 0.8;
    }
    </style>

    <div class="custom-domain-header">
        <h2 style="margin: 0; font-size: 2rem;">
            <i class="fas fa-globe"></i> Custom Domain Management
        </h2>
        <p style="margin: 15px 0 0 0; opacity: 0.9; font-size: 1.1rem;">
            Use your own branded domain (like <strong>yourcompany.com</strong>) instead of interrandadmin.com
        </p>
    </div>

    <!-- Modern Stats -->
    <div class="domain-stats-modern">
        <div class="row">
            <div class="col-md-3 col-sm-6">
                <div class="stat-item-modern">
                    <span class="stat-number-modern">
                        {{ \Modules\Superadmin\Entities\CustomDomain::where('business_id', session()->get('user.business_id'))->count() }}
                    </span>
                    <span class="stat-label-modern">Total Domains</span>
                </div>
            </div>
            <div class="col-md-3 col-sm-6">
                <div class="stat-item-modern">
                    <span class="stat-number-modern text-success">
                        {{ \Modules\Superadmin\Entities\CustomDomain::where('business_id', session()->get('user.business_id'))->verified()->count() }}
                    </span>
                    <span class="stat-label-modern">Active</span>
                </div>
            </div>
            <div class="col-md-3 col-sm-6">
                <div class="stat-item-modern">
                    <span class="stat-number-modern text-info">
                        {{ \Modules\Superadmin\Entities\CustomDomain::where('business_id', session()->get('user.business_id'))->sslActive()->count() }}
                    </span>
                    <span class="stat-label-modern">SSL Secured</span>
                </div>
            </div>
            <div class="col-md-3 col-sm-6">
                <div class="stat-item-modern">
                    <span class="stat-number-modern text-warning">
                        {{ \Modules\Superadmin\Entities\CustomDomain::getMaxDomainsForBusiness(session()->get('user.business_id')) - \Modules\Superadmin\Entities\CustomDomain::where('business_id', session()->get('user.business_id'))->count() }}
                    </span>
                    <span class="stat-label-modern">Remaining</span>
                </div>
            </div>
        </div>
    </div>

    <div class="row">
        <div class="col-xs-12">
            <div class="box box-primary">
                <div class="box-header with-border">
                    <h3 class="box-title"><i class="fas fa-list"></i> Your Custom Domains</h3>
                    <div class="box-tools pull-right">
                        @if(\Modules\Superadmin\Entities\CustomDomain::canAddMoreDomains(session()->get('user.business_id')))
                        <a href="{{ route('business.custom-domains.index') }}" class="btn btn-primary btn-sm">
                            <i class="fas fa-plus"></i> Add Domain
                        </a>
                        @else
                        <button class="btn btn-default btn-sm" disabled>
                            <i class="fas fa-lock"></i> Limit Reached
                        </button>
                        @endif
                    </div>
                </div>
                <div class="box-body">

            <!-- Domains List -->
            <div class="tw-bg-white tw-rounded-lg tw-shadow-md tw-overflow-hidden">
                @php
                    $domains = \Modules\Superadmin\Entities\CustomDomain::where('business_id', session()->get('user.business_id'))->latest()->get();
                @endphp

                @if($domains->count() > 0)
                    <div class="tw-overflow-x-auto">
                        <table class="tw-w-full tw-table-auto">
                            <thead class="tw-bg-gray-50">
                                <tr>
                                    <th class="tw-px-6 tw-py-3 tw-text-left tw-text-xs tw-font-medium tw-text-gray-500 tw-uppercase tw-tracking-wider">
                                        Domain
                                    </th>
                                    <th class="tw-px-6 tw-py-3 tw-text-left tw-text-xs tw-font-medium tw-text-gray-500 tw-uppercase tw-tracking-wider">
                                        Status
                                    </th>
                                    <th class="tw-px-6 tw-py-3 tw-text-left tw-text-xs tw-font-medium tw-text-gray-500 tw-uppercase tw-tracking-wider">
                                        SSL
                                    </th>
                                    <th class="tw-px-6 tw-py-3 tw-text-left tw-text-xs tw-font-medium tw-text-gray-500 tw-uppercase tw-tracking-wider">
                                        Added
                                    </th>
                                    <th class="tw-px-6 tw-py-3 tw-text-right tw-text-xs tw-font-medium tw-text-gray-500 tw-uppercase tw-tracking-wider">
                                        Actions
                                    </th>
                                </tr>
                            </thead>
                            <tbody class="tw-bg-white tw-divide-y tw-divide-gray-200">
                                @foreach($domains as $domain)
                                <tr class="tw-hover:bg-gray-50">
                                    <td class="tw-px-6 tw-py-4 tw-whitespace-nowrap">
                                        <div class="tw-flex tw-items-center">
                                            <i class="fas fa-globe tw-text-blue-500 tw-mr-3"></i>
                                            <div>
                                                <div class="tw-text-sm tw-font-medium tw-text-gray-900">
                                                    {{ $domain->custom_domain }}
                                                </div>
                                                @if($domain->domain_verified)
                                                <div class="tw-text-xs tw-text-green-600">
                                                    <i class="fas fa-check-circle tw-mr-1"></i>Verified {{ $domain->verified_at->diffForHumans() }}
                                                </div>
                                                @endif
                                            </div>
                                        </div>
                                    </td>
                                    <td class="tw-px-6 tw-py-4 tw-whitespace-nowrap">
                                        {!! $domain->getStatusBadge() !!}
                                    </td>
                                    <td class="tw-px-6 tw-py-4 tw-whitespace-nowrap">
                                        {!! $domain->getSslStatusBadge() !!}
                                    </td>
                                    <td class="tw-px-6 tw-py-4 tw-whitespace-nowrap tw-text-sm tw-text-gray-500">
                                        {{ $domain->created_at->format('M d, Y') }}
                                    </td>
                                    <td class="tw-px-6 tw-py-4 tw-whitespace-nowrap tw-text-right tw-text-sm tw-font-medium">
                                        <a href="{{ route('business.custom-domains.index') }}"
                                           class="tw-text-blue-600 hover:tw-text-blue-900 tw-mr-3">
                                            <i class="fas fa-cog tw-mr-1"></i>Manage
                                        </a>
                                    </td>
                                </tr>
                                @endforeach
                            </tbody>
                        </table>
                    </div>
                @else
                    <div class="tw-p-8 tw-text-center">
                        <div class="tw-text-gray-400 tw-mb-4">
                            <i class="fas fa-globe tw-text-6xl"></i>
                        </div>
                        <h3 class="tw-text-lg tw-font-medium tw-text-gray-900 tw-mb-2">No Custom Domains</h3>
                        <p class="tw-text-gray-500 tw-mb-4">
                            You haven't added any custom domains yet. Get started by adding your first domain.
                        </p>
                        @if(\Modules\Superadmin\Entities\CustomDomain::canAddMoreDomains(session()->get('user.business_id')))
                        <a href="{{ route('business.custom-domains.index') }}" class="tw-dw-btn tw-dw-btn-primary">
                            <i class="fas fa-plus tw-mr-2"></i>Add Your First Domain
                        </a>
                        @endif
                    </div>
                @endif
            </div>

            <!-- How it Works Section -->
            <div class="tw-mt-8 tw-bg-blue-50 tw-border tw-border-blue-200 tw-rounded-lg tw-p-6">
                <h4 class="tw-text-lg tw-font-semibold tw-text-blue-800 tw-mb-4">
                    <i class="fas fa-question-circle tw-mr-2"></i>How Custom Domains Work
                </h4>
                <div class="tw-grid tw-grid-cols-1 md:tw-grid-cols-2 lg:tw-grid-cols-4 tw-gap-4">
                    <div class="tw-text-center">
                        <div class="tw-bg-blue-100 tw-rounded-full tw-w-12 tw-h-12 tw-flex tw-items-center tw-justify-center tw-mx-auto tw-mb-3">
                            <i class="fas fa-plus tw-text-blue-600"></i>
                        </div>
                        <h5 class="tw-font-semibold tw-text-blue-800 tw-mb-1">1. Add Domain</h5>
                        <p class="tw-text-sm tw-text-blue-600">Enter your custom domain name</p>
                    </div>
                    <div class="tw-text-center">
                        <div class="tw-bg-blue-100 tw-rounded-full tw-w-12 tw-h-12 tw-flex tw-items-center tw-justify-center tw-mx-auto tw-mb-3">
                            <i class="fas fa-cog tw-text-blue-600"></i>
                        </div>
                        <h5 class="tw-font-semibold tw-text-blue-800 tw-mb-1">2. Configure DNS</h5>
                        <p class="tw-text-sm tw-text-blue-600">Set up DNS records with your provider</p>
                    </div>
                    <div class="tw-text-center">
                        <div class="tw-bg-blue-100 tw-rounded-full tw-w-12 tw-h-12 tw-flex tw-items-center tw-justify-center tw-mx-auto tw-mb-3">
                            <i class="fas fa-check tw-text-blue-600"></i>
                        </div>
                        <h5 class="tw-font-semibold tw-text-blue-800 tw-mb-1">3. Verify</h5>
                        <p class="tw-text-sm tw-text-blue-600">Confirm domain ownership</p>
                    </div>
                    <div class="tw-text-center">
                        <div class="tw-bg-blue-100 tw-rounded-full tw-w-12 tw-h-12 tw-flex tw-items-center tw-justify-center tw-mx-auto tw-mb-3">
                            <i class="fas fa-lock tw-text-blue-600"></i>
                        </div>
                        <h5 class="tw-font-semibold tw-text-blue-800 tw-mb-1">4. SSL Ready</h5>
                        <p class="tw-text-sm tw-text-blue-600">Secure with SSL certificate</p>
                    </div>
                </div>
            </div>
        </div>
    </div>
</div>
